---
import { getCompleteMenu } from '../utils/menuParser.ts';
import Navbar from './Navbar.astro';

// Get the menu data - this will read menu.md and process @include: directives
const menuData = getCompleteMenu();

// Function to extract the main title from markdown content
function extractTitle(markdown: string): string {
  const match = markdown.match(/^# (.+)$/m);
  return match ? match[1] : 'Untitled Product';
}

// Function to create a URL-friendly slug from a title
function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

// Function to extract images from markdown
function extractImages(markdown: string): string[] {
  const imageRegex = /!\[([^\]]*)\]\(([^)]+)(?:\s+"([^"]*)")?\)(?:\s*\{(\d+)(?::(\d+))?\})?/g;
  const images: string[] = [];
  let match;

  while ((match = imageRegex.exec(markdown)) !== null) {
    const [fullMatch, alt, src, title, width, height] = match;
    let imgTag = `<img src="${src}" alt="${alt}"`;
    if (title && title.trim()) {
      imgTag += ` title="${title}"`;
    }
    if (width) {
      if (height) {
        imgTag += ` style="width: ${width}px; height: ${height}px;"`;
      } else {
        imgTag += ` style="width: ${width}px; height: auto;"`;
      }
    }
    imgTag += ' />';
    images.push(imgTag);
  }

  return images;
}

// Function to process callouts in markdown
function processCallouts(markdown: string): string {
  const lines = markdown.split('\n');
  const result: string[] = [];
  let inCallout = false;
  let calloutType = '';
  let calloutTitle = '';
  let calloutContent: string[] = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // Check for callout start
    const calloutMatch = line.match(/^> \[!(NOTE|TIP|IMPORTANT|WARNING|CAUTION|INFO|SUCCESS|ERROR|DANGER)\]\s*(.*?)$/);
    if (calloutMatch) {
      // If we were already in a callout, close it first
      if (inCallout) {
        result.push(`<div class="callout callout-${calloutType}"><div class="callout-title"><span class="callout-icon">${getCalloutIcon(calloutType)}</span>${calloutTitle}</div><div class="callout-content">${calloutContent.join('<br>')}</div></div>`);
        calloutContent = [];
      }

      inCallout = true;
      calloutType = calloutMatch[1].toLowerCase();
      calloutTitle = calloutMatch[2] || calloutMatch[1];
    }
    // Check for callout content line
    else if (inCallout && line.startsWith('> ')) {
      calloutContent.push(line.substring(2));
    }
    // End of callout or regular line
    else {
      if (inCallout) {
        result.push(`<div class="callout callout-${calloutType}"><div class="callout-title"><span class="callout-icon">${getCalloutIcon(calloutType)}</span>${calloutTitle}</div><div class="callout-content">${calloutContent.join('<br>')}</div></div>`);
        inCallout = false;
        calloutContent = [];
      }
      result.push(line);
    }
  }

  // Close any remaining callout
  if (inCallout) {
    result.push(`<div class="callout callout-${calloutType}"><div class="callout-title"><span class="callout-icon">${getCalloutIcon(calloutType)}</span>${calloutTitle}</div><div class="callout-content">${calloutContent.join('<br>')}</div></div>`);
  }

  return result.join('\n');
}

// Function to convert markdown to HTML with ID support (images removed)
function markdownToHtml(markdown: string, productId?: string): string {
  // Process callouts first
  let processedMarkdown = processCallouts(markdown);

  let html = processedMarkdown
    // Remove images first (they'll be handled separately)
    .replace(/!\[([^\]]*)\]\(([^)]+)(?:\s+"([^"]*)")?\)(?:\s*\{(\d+)(?::(\d+))?\})?/g, '')
    // Headers with ID for main title
    .replace(/^# (.*$)/gm, (_, title) => {
      if (productId) {
        return `<h1 id="${productId}">${title}</h1>`;
      }
      return `<h1>${title}</h1>`;
    })
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    // Bold
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
    // Auto-link URLs (must come after manual links to avoid conflicts)
    .replace(/(^|[^"'>])(https?:\/\/[^\s<>"']+)/g, '$1<a href="$2" target="_blank" rel="noopener noreferrer">$2</a>')
    // Lists
    .replace(/^- (.*$)/gm, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
    // Paragraphs (skip HTML elements and empty lines)
    .replace(/^(?!<|^\s*$)(.*$)/gm, '<p>$1</p>')
    // Clean up empty paragraphs
    .replace(/<p><\/p>/g, '')
    .replace(/<p>\s*<\/p>/g, '');

  return html;
}

// Function to get callout icons
function getCalloutIcon(type: string): string {
  const icons = {
    note: '📝',
    tip: '💡',
    important: '❗',
    warning: '⚠️',
    caution: '⚠️',
    info: 'ℹ️',
    success: '✅',
    error: '❌',
    danger: '🚨'
  };
  return icons[type] || '📝';
}

// Generate TOC data from all products in all categories
const allProducts = menuData.sections
  .filter(section => section.type === 'category')
  .flatMap(section => section.products || []);

const tocItems = allProducts.map((product) => {
  const title = extractTitle(product.content);
  const slug = createSlug(title);
  return {
    title,
    slug,
    category: product.category,
    categoryIndex: product.categoryIndex
  };
});

// Group TOC items by category
const tocSections = tocItems.reduce((acc, item) => {
  if (!acc[item.category]) {
    acc[item.category] = [];
  }
  acc[item.category].push(item);
  return acc;
}, {} as Record<string, typeof tocItems>);

// Sort categories by categoryIndex
const sortedTocSections = Object.keys(tocSections)
  .sort((a, b) => {
    const aIndex = tocSections[a][0]?.categoryIndex || 0;
    const bIndex = tocSections[b][0]?.categoryIndex || 0;
    return aIndex - bIndex;
  })
  .reduce((acc, key) => {
    acc[key] = tocSections[key];
    return acc;
  }, {} as Record<string, typeof tocItems>);
---

<!-- Sticky Navbar -->
<Navbar />

<div class="menu-container" id="top">
  <!-- Dark mode toggle -->
  <div class="theme-toggle-container">
    <button id="theme-toggle" class="theme-toggle" aria-label="Toggle dark mode">
      <span class="theme-icon light-icon">☀️</span>
      <span class="theme-icon dark-icon">🌙</span>
    </button>
  </div>

  <!-- Render content sections first (main heading and intro text) -->
  <div class="menu-content">
    {menuData.sections.map((section) => (
      section.type === 'content' && (
        <div class="content-section">
          <div set:html={markdownToHtml(section.content || '')}></div>
        </div>
      )
    ))}
  </div>

  <!-- Table of Contents (after intro, before categories) -->
  {tocItems.length > 0 && (
    <div class="toc-section">
      <h2>📋 Table of Contents</h2>
      <nav class="toc-nav">
        <div class="toc-columns">
          {Object.entries(sortedTocSections).map(([sectionName, items], sectionIndex) => {
            // Skip the stacked categories and Shop Information here, we'll handle them separately
            if (sectionName === '🎢 Stimulants' || sectionName === '🤭 Downers' || sectionName === 'ℹ️ Shop Information') {
              return null;
            }

            return (
              <div class="toc-column">
                <div class="toc-section-group">
                  <h3 class="toc-section-title">{sectionIndex + 1}. {sectionName}</h3>
                  <ul class="toc-list">
                    {items.map((item, itemIndex) => (
                      <li class="toc-item">
                        <a href={`#${item.slug}`} class="toc-link">
                          {sectionIndex + 1}.{itemIndex + 1} {item.title}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            );
          })}

          <!-- Stacked categories column (now column 3) -->
          <div class="toc-column toc-stacked-column">
            {Object.entries(sortedTocSections).map(([sectionName, items], sectionIndex) => {
              // Only render the stacked categories
              if (sectionName !== '🎢 Stimulants' && sectionName !== '🤭 Downers') {
                return null;
              }

              return (
                <div class="toc-section-group">
                  <h3 class="toc-section-title">{sectionIndex + 1}. {sectionName}</h3>
                  <ul class="toc-list">
                    {items.map((item, itemIndex) => (
                      <li class="toc-item">
                        <a href={`#${item.slug}`} class="toc-link">
                          {sectionIndex + 1}.{itemIndex + 1} {item.title}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              );
            })}
          </div>

          <!-- Shop Information column (now column 4) -->
          {Object.entries(sortedTocSections).map(([sectionName, items], sectionIndex) => {
            // Only render Shop Information
            if (sectionName !== 'ℹ️ Shop Information') {
              return null;
            }

            return (
              <div class="toc-column">
                <div class="toc-section-group">
                  <h3 class="toc-section-title">{sectionIndex + 1}. {sectionName}</h3>
                  <ul class="toc-list">
                    {items.map((item, itemIndex) => (
                      <li class="toc-item">
                        <a href={`#${item.slug}`} class="toc-link">
                          {sectionIndex + 1}.{itemIndex + 1} {item.title}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            );
          })}
        </div>
      </nav>
    </div>
  )}

  <!-- Render category sections -->
  <div class="categories-content">
    {menuData.sections.map((section) => (
      section.type === 'category' && (
        <div class="category-section" data-category-index={section.categoryIndex} data-category={section.categoryName} id={createSlug(section.categoryName || '')}>
          <h2 class="category-header">{section.categoryName}</h2>
          <div class="category-products">
            {section.products?.map((product, productIndex) => {
              const productSlug = createSlug(extractTitle(product.content));
              const productNumber = `${section.categoryIndex}.${productIndex + 1}`;
              const productTitle = extractTitle(product.content);
              const images = extractImages(product.content);
              const contentWithoutImages = markdownToHtml(product.content, productSlug);

              return (
                <div class="product-item" data-product-number={productNumber}>
                  <div class="product-title">
                    <h1 id={productSlug}>{productTitle}</h1>
                  </div>
                  <div class="product-columns">
                    <div class="product-content">
                      <div set:html={contentWithoutImages.replace(/<h1[^>]*>.*?<\/h1>/g, '')}></div>
                    </div>
                    <div class="product-images">
                      {images.map((img) => (
                        <div set:html={img}></div>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )
    ))}
  </div>
</div>

<style>
  /* CSS Custom Properties for Theme Colors */
  :root,
  :root[data-theme="light"] {
    /* Light theme colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #2c3e50;
    --text-secondary: #34495e;
    --text-muted: #555555;
    --accent-primary: #3498db;
    --accent-secondary: #2980b9;
    --border-light: #dee2e6;
    --border-medium: #e9ecef;
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.15);

    /* Callout colors - light theme */
    --callout-note-bg: #e3f2fd;
    --callout-note-border: #3498db;
    --callout-tip-bg: #e8f5e8;
    --callout-tip-border: #2ecc71;
    --callout-important-bg: #f3e5f5;
    --callout-important-border: #9b59b6;
    --callout-warning-bg: #fff3cd;
    --callout-warning-border: #f39c12;
    --callout-info-bg: #d1ecf1;
    --callout-info-border: #17a2b8;
    --callout-success-bg: #d4edda;
    --callout-success-border: #28a745;
    --callout-error-bg: #f8d7da;
    --callout-error-border: #dc3545;
  }

  /* Dark theme colors */
  :root[data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --text-primary: #e8e8e8;
    --text-secondary: #d0d0d0;
    --text-muted: #a0a0a0;
    --accent-primary: #4a9eff;
    --accent-secondary: #357abd;
    --border-light: #404040;
    --border-medium: #2d2d2d;
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.4);

    /* Callout colors - dark theme */
    --callout-note-bg: #1e3a5f;
    --callout-note-border: #4a9eff;
    --callout-tip-bg: #1e3d1e;
    --callout-tip-border: #4caf50;
    --callout-important-bg: #3d1e3d;
    --callout-important-border: #ba68c8;
    --callout-warning-bg: #3d3d1e;
    --callout-warning-border: #ff9800;
    --callout-info-bg: #1e3d3d;
    --callout-info-border: #26c6da;
    --callout-success-bg: #1e3d1e;
    --callout-success-border: #4caf50;
    --callout-error-bg: #3d1e1e;
    --callout-error-border: #f44336;
  }

  /* Apply theme to body */
  body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Theme toggle styles */
  .theme-toggle-container {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 1001; /* Above navbar */
  }

  .theme-toggle {
    background: var(--bg-secondary);
    border: 2px solid var(--border-light);
    border-radius: 50px;
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px var(--shadow-light);
    position: relative;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px var(--shadow-medium);
  }

  .theme-icon {
    font-size: 1.5rem;
    position: absolute;
    transition: opacity 0.3s ease, transform 0.3s ease;
  }

  .light-icon {
    opacity: 1;
    transform: rotate(0deg);
  }

  .dark-icon {
    opacity: 0;
    transform: rotate(180deg);
  }

  :root[data-theme="dark"] .light-icon {
    opacity: 0;
    transform: rotate(180deg);
  }

  :root[data-theme="dark"] .dark-icon {
    opacity: 1;
    transform: rotate(0deg);
  }

  .menu-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: system-ui, sans-serif;
  }

  .menu-content {
    margin-bottom: 2rem;
  }

  .categories-content {
    margin-top: 1rem;
  }

  .content-section {
    margin-bottom: 2rem;
  }

  .content-section h1 {
    color: var(--text-primary);
    border-bottom: 3px solid var(--accent-primary);
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
  }

  /* Category sections */
  .category-section {
    margin-bottom: 3rem;
  }

  .category-header {
    color: var(--text-primary);
    font-size: 1.8rem;
    margin-bottom: 2rem;
    padding-bottom: 0.75rem;
    border-bottom: 3px solid var(--accent-primary);
  }

  .category-products {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  /* Table of Contents Styles */
  .toc-section {
    background: var(--bg-secondary);
    border: 2px solid var(--border-medium);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 3rem;
    box-shadow: 0 2px 8px var(--shadow-light);
  }

  .toc-section h2 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    border-bottom: 2px solid var(--accent-primary);
    padding-bottom: 0.5rem;
  }

  .toc-nav {
    max-width: 100%;
  }

  /* Multi-column layout for TOC */
  .toc-columns {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 2rem;
    align-items: start;
  }

  .toc-column {
    min-width: 0; /* Prevents overflow issues */
  }

  /* Special handling for stacked categories column */
  .toc-stacked-column {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .toc-section-group {
    margin-bottom: 0;
  }

  .toc-section-title {
    color: var(--text-secondary);
    font-size: 1.2rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-light);
    font-weight: 600;
  }

  .toc-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .toc-item {
    margin: 0;
  }

  .toc-link {
    display: block;
    padding: 0.75rem 1rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 6px;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px var(--shadow-light);
  }

  .toc-link:hover {
    background: var(--accent-primary);
    color: var(--bg-primary);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px var(--shadow-medium);
    border-color: var(--accent-primary);
  }

  .toc-link:active {
    transform: translateY(0);
  }

  /* Category sections are now handled above */

  .product-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-medium);
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px var(--shadow-light);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
  }

  .product-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-medium);
  }

  .product-title {
    margin-bottom: 1.5rem;
  }

  .product-title h1 {
    color: var(--text-primary);
    font-size: 1.5rem;
    margin: 0;
    border-bottom: 2px solid var(--accent-primary);
    padding-bottom: 0.5rem;
    position: relative;
  }

  .product-item h3 {
    color: var(--text-primary);
    margin: 1.5rem 0 0.5rem 0;
    font-size: 1.2rem;
  }

  .product-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: start;
  }

  .product-content {
    min-width: 0; /* Prevents overflow issues */
  }

  .product-images {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  /* Callout Styles - Global to work with dynamically generated content */
  :global(.callout) {
    border-radius: 8px;
    margin: 1rem 0;
    padding: 1rem;
    border-left: 4px solid;
    background: var(--bg-secondary);
  }

  /* Callouts in product content should be 60% of column width */
  :global(.product-content .callout) {
    width: 60%;
    max-width: 60%;
    box-sizing: border-box;
  }

  /* Callouts in shop information sections should be wider */
  :global(.category-section[data-category="ℹ️ Shop Information"] .callout) {
    width: 85%;
    max-width: 85%;
    box-sizing: border-box;
  }

  :global(.callout-title) {
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
  }

  :global(.callout-icon) {
    font-size: 1.1em;
  }

  :global(.callout-content) {
    margin: 0;
    color: var(--text-primary);
  }

  /* Callout type-specific colors */
  :global(.callout-note) {
    border-left-color: var(--callout-note-border);
    background: var(--callout-note-bg);
  }

  :global(.callout-tip) {
    border-left-color: var(--callout-tip-border);
    background: var(--callout-tip-bg);
  }

  :global(.callout-important) {
    border-left-color: var(--callout-important-border);
    background: var(--callout-important-bg);
  }

  :global(.callout-warning), :global(.callout-caution) {
    border-left-color: var(--callout-warning-border);
    background: var(--callout-warning-bg);
  }

  :global(.callout-info) {
    border-left-color: var(--callout-info-border);
    background: var(--callout-info-bg);
  }

  :global(.callout-success) {
    border-left-color: var(--callout-success-border);
    background: var(--callout-success-bg);
  }

  :global(.callout-error), :global(.callout-danger) {
    border-left-color: var(--callout-error-border);
    background: var(--callout-error-bg);
  }

  /* Product numbering is handled by JavaScript */

  .product-item h2 {
    color: var(--text-secondary);
    font-size: 1.2rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  /* List styling - make global to override browser defaults */
  :global(.product-item ul) {
    margin: 0.5rem 0 1rem 0;
    padding-left: 0 !important;
    list-style: none !important;
  }

  :global(.product-item li) {
    margin-bottom: 0;
    line-height: 1.6;
    padding-left: 0 !important;
    margin-left: 0 !important;
    color: var(--text-primary);
  }

  .product-item strong {
    color: var(--text-primary);
  }

  /* Link styling - global to override browser defaults */
  :global(.product-item a) {
    color: var(--accent-primary) !important;
    text-decoration: none;
    font-weight: 500;
  }

  :global(.product-item a:hover) {
    text-decoration: underline;
    color: var(--accent-secondary) !important;
  }

  .product-item p {
    line-height: 1.6;
    margin-bottom: 1rem;
    color: var(--text-muted);
  }

  /* Remove margin/padding above first paragraph in product content - global to override defaults */
  :global(.product-content > div > p:first-child) {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }

  .product-images img {
    width: 100%;
    height: auto;
    border-radius: 5px;
    box-shadow: 0 2px 8px var(--shadow-light);
    display: block;
  }

  /* Images with custom dimensions should respect those dimensions but still be responsive */
  .product-images img[style*="width"] {
    max-width: 100%;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Add scroll margin to account for any fixed headers */
  .product-item h1[id] {
    scroll-margin-top: 2rem;
  }

  /* Responsive design */
  @media (max-width: 1024px) {
    /* Tablet layout - 2 columns */
    .toc-columns {
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
    }
  }

  @media (max-width: 768px) {
    .menu-container {
      padding: 1rem;
    }

    .product-item {
      padding: 1.5rem;
    }

    .product-columns {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .product-images {
      order: -1; /* Move images above content on mobile */
    }

    .toc-section {
      padding: 1.5rem;
      margin-bottom: 2rem;
    }

    /* Stack columns vertically on mobile */
    .toc-columns {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .toc-stacked-column {
      gap: 1rem;
    }

    .toc-link {
      padding: 0.75rem 1rem;
      font-size: 0.9rem;
    }

    /* Adjust theme toggle for mobile - position next to hamburger menu */
    .theme-toggle-container {
      top: 0.75rem;
      right: 4rem; /* Next to hamburger menu */
    }

    .theme-toggle {
      width: 50px;
      height: 50px;
      padding: 0.5rem;
    }

    .theme-icon {
      font-size: 1.2rem;
    }
  }
</style>

<script>
  // Theme management
  function initTheme() {
    // Check for saved theme preference or default to system preference
    const savedTheme = localStorage.getItem('theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const initialTheme = savedTheme || (systemPrefersDark ? 'dark' : 'light');

    // Apply theme
    document.documentElement.setAttribute('data-theme', initialTheme);

    // Update toggle button state
    updateToggleButton(initialTheme);
  }

  function updateToggleButton(theme) {
    const toggle = document.getElementById('theme-toggle');
    if (toggle) {
      toggle.setAttribute('aria-label', theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode');
    }
  }

  function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    // Apply new theme
    document.documentElement.setAttribute('data-theme', newTheme);

    // Save preference
    localStorage.setItem('theme', newTheme);

    // Update toggle button
    updateToggleButton(newTheme);
  }

  // Add category and product numbering
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize theme
    initTheme();

    // Add theme toggle event listener
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
      themeToggle.addEventListener('click', toggleTheme);
    }

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      // Only auto-switch if user hasn't manually set a preference
      if (!localStorage.getItem('theme')) {
        const newTheme = e.matches ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', newTheme);
        updateToggleButton(newTheme);
      }
    });

    // Add category numbering to category headers
    const categoryHeaders = document.querySelectorAll('.category-header');
    categoryHeaders.forEach((header, index) => {
      const categoryNumber = index + 1;
      header.textContent = `${categoryNumber}. ${header.textContent}`;
    });

    // Add product numbering to product items
    const productItems = document.querySelectorAll('.product-item[data-product-number]');
    productItems.forEach((productItem) => {
      const productNumber = productItem.getAttribute('data-product-number');
      const titleH1 = productItem.querySelector('.product-title h1');
      if (titleH1 && productNumber) {
        titleH1.textContent = `${productNumber} ${titleH1.textContent}`;
      }
    });
  });
</script>
